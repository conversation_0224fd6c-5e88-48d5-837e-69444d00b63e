import { useState } from 'react';
import { supabase } from '../../lib/supabase';
import { LoadingSpinner, SuccessMessage, ErrorMessage } from '../ui/StatusMessages';

interface TestEmailButtonProps {
  certificateId: string;
  emailType: 'customer_success' | 'customer_failure' | 'admin_success' | 'admin_failure';
  onSuccess?: () => void;
  className?: string;
}

interface TestEmailResponse {
  success: boolean;
  message: string;
  certificate_id: string;
  email_type: string;
  error?: string;
}

export const TestEmailButton = ({ 
  certificateId, 
  emailType, 
  onSuccess,
  className = ''
}: TestEmailButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const emailTypeLabels = {
    customer_success: 'Kunde - Erfolg',
    customer_failure: 'Kunde - Fehlschlag',
    admin_success: 'Admin - Erfolg',
    admin_failure: 'Admin - Fehlschlag'
  };

  const handleTestEmail = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const { data, error } = await supabase.functions.invoke('test-email', {
        body: {
          certificate_id: certificateId,
          email_type: emailType
        }
      });

      if (error) {
        throw new Error(error.message || 'Fehler beim Aufrufen der Test-E-Mail-Funktion');
      }

      const response = data as TestEmailResponse;
      
      if (response.success) {
        setResult({ success: true, message: response.message });
        onSuccess?.();
      } else {
        setResult({ 
          success: false, 
          message: response.error || response.message || 'Unbekannter Fehler beim Senden der Test-E-Mail'
        });
      }
    } catch (error) {
      console.error('Error testing email:', error);
      setResult({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Unbekannter Fehler beim Senden der Test-E-Mail'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <button
        onClick={handleTestEmail}
        disabled={isLoading}
        className={`
          inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md
          ${isLoading 
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
            : 'bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
          }
          transition-colors duration-200
        `}
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sende...
          </>
        ) : (
          `Test ${emailTypeLabels[emailType]}`
        )}
      </button>

      {result && (
        <div className="mt-3">
          {result.success ? (
            <SuccessMessage message={result.message} />
          ) : (
            <ErrorMessage message={result.message} />
          )}
        </div>
      )}
    </div>
  );
};
