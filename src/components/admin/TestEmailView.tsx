import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { LoadingSpinner, ErrorMessage } from '../ui/StatusMessages';
import { TestEmailButton } from './TestEmailButton';

interface Certificate {
  id: string;
  order_number: string | null;
  certificate_type: string | null;
  payment_status: string | null;
  created_at: string;
}

export const TestEmailView = () => {
  const [selectedCertificateId, setSelectedCertificateId] = useState<string>('');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch certificates for the dropdown
  const { data: certificates, isLoading, error } = useQuery({
    queryKey: ['certificates-for-email-test'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('id, order_number, certificate_type, payment_status, created_at')
        .order('created_at', { ascending: false })
        .limit(50); // Limit to recent certificates

      if (error) throw error;
      return data as Certificate[];
    },
  });

  const handleEmailTestSuccess = () => {
    // Trigger a refresh of email logs if needed
    setRefreshKey(prev => prev + 1);
  };

  if (isLoading) {
    return (
      <div className="py-8">
        <LoadingSpinner message="Lade Zertifikate..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-4">
        <ErrorMessage message="Fehler beim Laden der Zertifikate für E-Mail-Tests." />
      </div>
    );
  }

  const selectedCertificate = certificates?.find(cert => cert.id === selectedCertificateId);

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">E-Mail-Funktionalität testen</h2>
        
        <div className="space-y-6">
          {/* Certificate Selection */}
          <div>
            <label htmlFor="certificate-select" className="block text-sm font-medium text-gray-700 mb-2">
              Zertifikat auswählen
            </label>
            <select
              id="certificate-select"
              value={selectedCertificateId}
              onChange={(e) => setSelectedCertificateId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">-- Zertifikat auswählen --</option>
              {certificates?.map((cert) => (
                <option key={cert.id} value={cert.id}>
                  {cert.order_number || cert.id.slice(-8)} - {cert.certificate_type} - {cert.payment_status} 
                  ({new Date(cert.created_at).toLocaleDateString('de-DE')})
                </option>
              ))}
            </select>
            {certificates && certificates.length === 0 && (
              <p className="text-sm text-gray-500 mt-1">
                Keine Zertifikate gefunden. Erstellen Sie zuerst ein Zertifikat, um E-Mails zu testen.
              </p>
            )}
          </div>

          {/* Selected Certificate Info */}
          {selectedCertificate && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Ausgewähltes Zertifikat</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">ID:</span>
                  <span className="ml-2 text-gray-900">{selectedCertificate.id}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Bestellnummer:</span>
                  <span className="ml-2 text-gray-900">{selectedCertificate.order_number || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Typ:</span>
                  <span className="ml-2 text-gray-900">{selectedCertificate.certificate_type}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Zahlungsstatus:</span>
                  <span className="ml-2 text-gray-900">{selectedCertificate.payment_status}</span>
                </div>
              </div>
            </div>
          )}

          {/* Test Email Buttons */}
          {selectedCertificateId && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">E-Mail-Tests</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="text-md font-medium text-gray-700">Kunden-E-Mails</h4>
                  <TestEmailButton
                    certificateId={selectedCertificateId}
                    emailType="customer_success"
                    onSuccess={handleEmailTestSuccess}
                    className="w-full"
                  />
                  <TestEmailButton
                    certificateId={selectedCertificateId}
                    emailType="customer_failure"
                    onSuccess={handleEmailTestSuccess}
                    className="w-full"
                  />
                </div>
                <div className="space-y-3">
                  <h4 className="text-md font-medium text-gray-700">Admin-E-Mails</h4>
                  <TestEmailButton
                    certificateId={selectedCertificateId}
                    emailType="admin_success"
                    onSuccess={handleEmailTestSuccess}
                    className="w-full"
                  />
                  <TestEmailButton
                    certificateId={selectedCertificateId}
                    emailType="admin_failure"
                    onSuccess={handleEmailTestSuccess}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-green-900 mb-2">Hinweise</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• Wählen Sie ein Zertifikat aus der Liste aus, um E-Mail-Tests durchzuführen</li>
              <li>• Die Test-E-Mails werden an die konfigurierten E-Mail-Adressen gesendet</li>
              <li>• Überprüfen Sie die E-Mail-Logs nach dem Test, um den Status zu sehen</li>
              <li>• Stellen Sie sicher, dass die Resend-API-Konfiguration korrekt ist</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
