import { useField } from '@tanstack/react-form';

interface RadioFieldProps<T> {
  name: keyof T;
  label: string;
  required?: boolean;
  form: any; // Form instance from Tanstack Form
}

export const RadioField = <T,>({
  name,
  label,
  required = false,
  form
}: RadioFieldProps<T>) => {
  const { state, handleChange } = useField({
    name: String(name),
    form,
  });

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="flex space-x-4">
        <label className="flex items-center">
          <input
            type="radio"
            name={name as string}
            value="0"
            checked={state.value === '0'}
            onChange={(e) => handleChange(e.target.value)}
            className="mr-2 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
          />
          Nein
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            name={name as string}
            value="1"
            checked={state.value === '1'}
            onChange={(e) => handleChange(e.target.value)}
            className="mr-2 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
          />
          Ja
        </label>
      </div>
      {state.meta.errors.length > 0 && (
        <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
      )}
    </div>
  );
};
