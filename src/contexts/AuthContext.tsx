import { createContext, useContext, useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import type { Session, User } from '@supabase/supabase-js';
import { supabase, getAuthRedirectUrl } from '../lib/supabase';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signUp: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{
    error: Error | null;
    data: any;
  }>;
  updatePassword: (newPassword: string) => Promise<{
    error: Error | null;
    data: { user: User | null } | null;
  }>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => {
      data?.subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      return await supabase.auth.signInWithPassword({ email, password });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  };

  const signUp = async (email: string, password: string) => {
    setLoading(true);
    try {
      return await supabase.auth.signUp({ email, password });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      await supabase.auth.signOut();
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  };

  const resetPassword = async (email: string) => {
    setLoading(true);
    try {
      const redirectTo = `${getAuthRedirectUrl()}reset-password`;
      return await supabase.auth.resetPasswordForEmail(email, {
        redirectTo,
      });
    } finally {
      setLoading(false); // We set loading to false here since this doesn't trigger onAuthStateChange
    }
  };

  const updatePassword = async (newPassword: string) => {
    setLoading(true);
    try {
      return await supabase.auth.updateUser({
        password: newPassword,
      });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  };

  const value = {
    session,
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
