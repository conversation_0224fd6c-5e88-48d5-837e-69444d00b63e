import { Link, useNavigate } from '@tanstack/react-router';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { useCertificate } from '../contexts/CertificateContext';
import { ActiveCertificateIndicator } from '../components/ui/ActiveCertificateIndicator';

// Define the certificate types
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define the page types
type PageType = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';

// Define the page configuration for each certificate type
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung', // Only WG/B type has the heizung page
    'tww-lueftung', // Only WG/B type has the tww-lueftung page
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

// Define the page titles and descriptions
const pageInfo: Record<PageType, {
  title: string;
  description: string;
  linkText: string;
  path: string;
}> = {
  'objektdaten': {
    title: 'Allgemeine Objektdaten & Kundendaten',
    description: 'Erfassen Sie die grundlegenden Daten zum Objekt und zum Kunden.',
    linkText: 'Objektdaten erfassen',
    path: '/erfassen/objektdaten'
  },
  'gebaeudedetails1': {
    title: 'Gebäudedaten (Teil 1)',
    description: 'Erfassen Sie die grundlegenden technischen Daten zum Gebäude (Baujahr, Wohnfläche, etc.).',
    linkText: 'Gebäudedaten Teil 1',
    path: '/erfassen/gebaeudedetails1'
  },
  'gebaeudedetails2': {
    title: 'Gebäudedaten (Teil 2)',
    description: 'Erfassen Sie Daten zur Gebäudehülle, Dämmung und Lüftung.',
    linkText: 'Gebäudedaten Teil 2',
    path: '/erfassen/gebaeudedetails2'
  },
  'fenster': {
    title: 'Fenster & Verglasung',
    description: 'Erfassen Sie die Daten zu Fenstern und Verglasungen des Gebäudes.',
    linkText: 'Fenster erfassen',
    path: '/erfassen/fenster'
  },
  'heizung': {
    title: 'Heizungssystem',
    description: 'Erfassen Sie die Daten zum Heizungssystem und zur Energieversorgung.',
    linkText: 'Heizungssystem erfassen',
    path: '/erfassen/heizung'
  },
  'tww-lueftung': {
    title: 'Trinkwarmwasser & Lüftung',
    description: 'Erfassen Sie die Daten zu Trinkwarmwasser und Lüftung.',
    linkText: 'TWW & Lüftung erfassen',
    path: '/erfassen/tww-lueftung'
  },
  'verbrauch': {
    title: 'Energieverbrauch',
    description: 'Erfassen Sie die Verbrauchsdaten für bis zu drei Energieträger und drei Jahre. Laden Sie auch Ihre Verbrauchsrechnungen hoch.',
    linkText: 'Verbrauchsdaten erfassen',
    path: '/erfassen/verbrauch'
  },
  'zusammenfassung': {
    title: 'Zusammenfassung',
    description: 'Überprüfen Sie alle eingegebenen Daten und generieren Sie den Energieausweis.',
    linkText: 'Zur Zusammenfassung',
    path: '/erfassen/zusammenfassung'
  }
};

export const ErfassenPage = () => {
  const navigate = useNavigate();
  const [certificateType, setCertificateType] = useState<CertificateType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { activeCertificateId, loading: certificateLoading } = useCertificate();

  // Fetch the certificate data using the active certificate ID
  const { data: certificateData, isLoading: isCertificateDataLoading } = useQuery({
    queryKey: ['energieausweise', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('id', activeCertificateId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!activeCertificateId && !certificateLoading,
    retry: false,
  });

  // Redirect to certificate selection if no active certificate
  useEffect(() => {
    if (!certificateLoading && !activeCertificateId) {
      navigate({ to: '/meine-zertifikate' });
    }
  }, [activeCertificateId, certificateLoading, navigate]);

  // Set certificate type from fetched data
  useEffect(() => {
    if (certificateData) {
      if (certificateData.certificate_type) {
        setCertificateType(certificateData.certificate_type as CertificateType);
      } else {
        navigate({ to: '/erfassen/certificate-type' });
      }
    }
    setIsLoading(false);
  }, [certificateData, navigate]);

  // If loading or no certificate type, show loading state
  if (isLoading || isCertificateDataLoading || certificateLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">
          Energieausweis erfassen
        </h1>
        <div className="flex items-center justify-center p-12">
          <div className="w-12 h-12 border-t-4 border-green-500 border-solid rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-600">Wird geladen...</span>
        </div>
      </div>
    );
  }

  // If no certificate type is selected, show a message
  if (!certificateType) {
    return (
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">
          Energieausweis erfassen
        </h1>
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Bitte wählen Sie zuerst einen Energieausweis-Typ aus.
              </p>
            </div>
          </div>
        </div>
        <div className="text-center">
          <Link
            to="/erfassen/certificate-type"
            className="inline-block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-lg font-medium"
          >
            Energieausweis-Typ auswählen
          </Link>
        </div>
      </div>
    );
  }

  // Get the pages for the selected certificate type
  const pages: PageType[] = certificateTypePages[certificateType] || [];

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Energieausweis erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Hier können Sie alle relevanten Daten für Ihren Energieausweis eingeben.
        Folgen Sie den Schritten, um alle erforderlichen Informationen zu erfassen.
      </p>

      <ActiveCertificateIndicator />

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Energieausweis Typ: {certificateType === 'WG/V' ? 'Wohngebäude-Verbrauchsausweis' :
                            certificateType === 'WG/B' ? 'Wohngebäude-Bedarfsausweis' :
                            'Nicht-Wohngebäude-Verbrauchsausweis'}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {pages.map((page) => {
          const info = pageInfo[page];
          if (!info) return null;

          return (
            <div key={page} className="bg-white p-6 rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow flex flex-col h-full">
              <h2 className="text-xl font-semibold text-gray-800 mb-3">{info.title}</h2>
              <p className="text-gray-600 mb-4 flex-grow">
                {info.description}
              </p>
              <div className="mt-auto">
                <Link
                  to={info.path}
                  className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                >
                  {info.linkText}
                </Link>
              </div>
            </div>
          );
        })}
      </div>

    </div>
  );
};
