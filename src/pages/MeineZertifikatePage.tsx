import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { useCertificate } from '../contexts/CertificateContext';

// Define certificate status types
type CertificateStatus = 'in_progress' | 'completed' | 'paid';

// Define certificate type
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define certificate data structure
interface Certificate {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  certificate_type: CertificateType | null;
  payment_status: string | null;
  status: CertificateStatus;
  objektdaten?: Record<string, any> | null;
}

// Helper function to get certificate type display name
const getCertificateTypeName = (type: CertificateType | null): string => {
  switch (type) {
    case 'WG/V':
      return 'Wohngebäude-Verbrauchsausweis';
    case 'WG/B':
      return 'Wohngebäude-Bedarfsausweis';
    case 'NWG/V':
      return 'Nicht-Wohngebäude-Verbrauchsausweis';
    default:
      return 'Unbekannter Typ';
  }
};


// Helper function to get status display name
const getStatusName = (status: CertificateStatus | null, paymentStatus: string | null): string => {
  if (paymentStatus === 'paid') {
    return 'Bezahlt';
  }

  switch (status) {
    case 'in_progress':
      return 'In Bearbeitung';
    case 'completed':
      return 'Ausgefüllt';
    default:
      return 'Unbekannt';
  }
};

export const MeineZertifikatePage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState<string | null>(null);
  const { setActiveCertificateId } = useCertificate();

  // Clear success message after 3 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // Fetch all certificates for the current user
  const { data: certificates, isLoading, refetch } = useQuery({
    queryKey: ['energieausweise'],
    queryFn: async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('user_id', user.user.id)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data as Certificate[];
    },
  });

  // Delete certificate mutation
  const deleteMutation = useMutation({
    mutationFn: async (certificateId: string) => {
      console.log('Deleting certificate with ID:', certificateId);

      // Get the current user
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      // Delete the certificate
      const { error, count } = await supabase
        .from('energieausweise')
        .delete()
        .eq('id', certificateId)
        .eq('user_id', user.user.id); // Ensure we're only deleting the user's own certificates

      if (error) throw error;

      console.log(`Deleted ${count} certificate(s)`);
      return certificateId;
    },
    onSuccess: async (deletedId) => {
      console.log('Certificate deleted successfully:', deletedId);

      // Manually update the UI by filtering out the deleted certificate
      if (certificates) {
        const updatedCertificates = certificates.filter(cert => cert.id !== deletedId);
        queryClient.setQueryData(['energieausweise'], updatedCertificates);
      }

      // Also refetch to ensure we have the latest data
      await refetch();

      // Show success message
      setSuccess('Energieausweis wurde erfolgreich gelöscht');

      // Clear any previous error
      setError(null);

      // Close the confirmation dialog
      setShowConfirmDelete(null);
    },
    onError: (error) => {
      console.error('Error deleting certificate:', error);
      setError(`Fehler beim Löschen: ${error.message}`);
      setShowConfirmDelete(null); // Close the dialog even on error
    },
  });

  // Handle certificate deletion
  const handleDelete = (certificateId: string) => {
    setShowConfirmDelete(certificateId);
  };

  // Confirm certificate deletion
  const confirmDelete = (certificateId: string) => {
    if (certificateId) {
      console.log('Confirming deletion of certificate:', certificateId);
      try {
        deleteMutation.mutate(certificateId);
      } catch (err) {
        console.error('Error in delete mutation:', err);
        setError(`Fehler beim Löschen: ${err instanceof Error ? err.message : 'Unbekannter Fehler'}`);
      }
    } else {
      console.error('No certificate ID provided for deletion');
      setError('Keine Zertifikat-ID zum Löschen angegeben');
    }
  };

  // Handle creating a new certificate
  const handleCreateNew = () => {
    navigate({ to: '/erfassen/certificate-type' });
  };

  // Handle continuing with an existing certificate
  const handleContinue = (certificateId: string) => {
    // Set the active certificate ID in the context
    setActiveCertificateId(certificateId);

    // Invalidate any existing queries to ensure fresh data is loaded
    queryClient.invalidateQueries({ queryKey: ['energieausweise'] });

    // Navigate to the appropriate page based on progress
    navigate({ to: '/erfassen' });
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          Meine Energieausweise
        </h1>
        <button
          onClick={handleCreateNew}
          className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Neuen Energieausweis erstellen
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-8">
          <div className="w-12 h-12 border-t-4 border-green-500 border-solid rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Energieausweise werden geladen...</p>
        </div>
      ) : certificates && certificates.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Typ
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Erstellt am
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Zuletzt bearbeitet
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aktionen
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {certificates.map((certificate) => (
                <tr key={certificate.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {getCertificateTypeName(certificate.certificate_type)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {certificate.objektdaten?.Strasse && certificate.objektdaten?.Hausnr 
                        && certificate.objektdaten?.PLZ && certificate.objektdaten?.Ort
                        ? `${certificate.objektdaten.Strasse} ${certificate.objektdaten.Hausnr} ${certificate.objektdaten.PLZ} ${certificate.objektdaten.Ort} `
                        : 'Keine Adresse'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(certificate.created_at).toLocaleDateString('de-DE')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(certificate.updated_at).toLocaleDateString('de-DE')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${certificate.payment_status === 'paid'
                        ? 'bg-green-100 text-green-800'
                        : certificate.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'}`}>
                      {getStatusName(certificate.status, certificate.payment_status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleContinue(certificate.id)}
                      className="text-green-600 hover:text-green-900 mr-4"
                    >
                      Bearbeiten
                    </button>
                    <button
                      onClick={() => handleDelete(certificate.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Löschen
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Energieausweise vorhanden</h3>
          <p className="text-gray-600 mb-6">
            Sie haben noch keine Energieausweise erstellt. Klicken Sie auf den Button, um Ihren ersten Energieausweis zu erstellen.
          </p>
          <button
            onClick={handleCreateNew}
            className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Energieausweis erstellen
          </button>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Energieausweis löschen</h3>
            <p className="text-gray-600 mb-6">
              Sind Sie sicher, dass Sie diesen Energieausweis löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowConfirmDelete(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                type="button"
              >
                Abbrechen
              </button>
              <button
                onClick={() => {
                  console.log('Delete button clicked for certificate:', showConfirmDelete);
                  confirmDelete(showConfirmDelete);
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                type="button"
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Wird gelöscht...
                  </>
                ) : (
                  'Löschen'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
