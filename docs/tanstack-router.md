# Tanstack Router Documentation

## Introduction

[Tanstack Router](https://tanstack.com/router) is a modern, type-safe routing library for React applications. It provides a powerful and flexible way to handle routing in React applications with features like:

- Type safety with TypeScript
- Nested routing
- Route-based code splitting
- Route guards and middleware
- Search params and URL state management
- Preloading and prefetching

This document explains how Tanstack Router works in principle and how it's implemented in the Verbrauchsausweis App.

## How Tanstack Router Works

### Core Concepts

1. **Routes**: Routes are the building blocks of Tanstack Router. Each route represents a URL path and is associated with a React component.

2. **Route Tree**: Routes are organized in a hierarchical tree structure, where each route can have parent and child routes.

3. **Context**: Tanstack Router allows passing context data to routes, which can be accessed during route resolution.

4. **Loaders and Actions**: Routes can define loaders (for data fetching) and actions (for data mutations).

5. **Route Guards**: Routes can implement guards using `beforeLoad` to control access to routes.

6. **Outlet**: The `<Outlet />` component is used to render child routes within parent routes.

### Basic Implementation Pattern

```tsx
// Define a root route
const rootRoute = createRootRoute({
  component: RootComponent,
});

// Define child routes
const homeRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/',
  component: HomeComponent,
});

// Create the route tree
const routeTree = rootRoute.addChildren([
  homeRoute,
  // other routes
]);

// Create the router
const router = createRouter({
  routeTree,
});

// Use the router in your app
function App() {
  return <RouterProvider router={router} />;
}
```

## Implementation in Verbrauchsausweis App

### Router Setup

The Verbrauchsausweis App uses Tanstack Router with authentication integration. The router is defined in `src/routes/index.tsx` and used in `src/App.tsx`.

#### Router Context

The router is created with a context that includes the authenticated user and session:

```tsx
// Define the router context type
type RouterContext = {
  user: User | null;
  session: Session | null;
};

// Create the router
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  context: {
    user: null,
    session: null,
  },
});
```

#### Route Definition

Routes are defined with their paths, components, and access controls:

```tsx
const erfassenRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/erfassen',
  component: ErfassenPage,
  beforeLoad: ({ context }) => {
    if (!context.session) {
      throw redirect({
        to: '/login',
        search: {
          redirect: '/erfassen',
        },
      });
    }
  },
});
```

### Authentication Integration

The router is integrated with the authentication system through the `AuthContext`:

1. **Auth Context Provider**: The `AuthProvider` component manages authentication state and provides auth-related functions.

2. **Router Context Update**: When the authentication state changes, the router context is updated:

```tsx
function AppWithAuth() {
  const { user, session } = useAuth();

  useEffect(() => {
    router.update({
      context: {
        user,
        session,
      },
    });
  }, [user, session]);

  return <RouterProvider router={router} />;
}
```

### Protected Routes

The app implements protected routes using the `beforeLoad` hook:

```tsx
beforeLoad: ({ context }) => {
  if (!context.session) {
    throw redirect({
      to: '/login',
      search: {
        redirect: '/erfassen/objektdaten',
      },
    });
  }
}
```

This redirects unauthenticated users to the login page and preserves the intended destination in the search parameters.

### Route Structure

The app has the following route structure:

1. **Public Routes**:
   - Home (`/`)
   - Login (`/login`)
   - Register (`/register`)
   - Forgot Password (`/forgot-password`)

2. **Protected Routes** (require authentication):
   - Erfassen (`/erfassen`)
   - Objektdaten (`/erfassen/objektdaten`)
   - Gebäudedetails 1 (`/erfassen/gebaeudedetails1`)
   - Gebäudedetails 2 (`/erfassen/gebaeudedetails2`)
   - Zusammenfassung (`/zusammenfassung`)
   - Admin (`/admin`)
   - Konto (`/konto`)

3. **Special Routes**:
   - Reset Password (`/reset-password`) - Requires a valid session from the reset link
   - Not Found (`*`) - Catches all undefined routes

### Navigation

Navigation between routes is handled using the `useNavigate` hook and the `Link` component:

```tsx
// Using useNavigate hook
const navigate = useNavigate();
navigate({ to: '/erfassen/fenster' });

// Using Link component
<Link to="/erfassen/objektdaten" className="...">
  Weiter
</Link>
```

## Best Practices

1. **Type Safety**: Leverage TypeScript for type-safe routing.

2. **Route Organization**: Keep related routes together and use a logical hierarchy.

3. **Authentication Guards**: Use `beforeLoad` to protect routes that require authentication.

4. **Context Updates**: Keep the router context in sync with your application state.

5. **Redirect Handling**: When redirecting to login, include the original destination to redirect back after successful authentication.

## Conclusion

Tanstack Router provides a powerful and flexible routing solution for the Verbrauchsausweis App. Its integration with the authentication system ensures that protected routes are only accessible to authenticated users, while its type-safe nature helps prevent routing-related bugs.

The implementation in this project demonstrates how to:
- Define a route structure
- Protect routes with authentication
- Pass context data to routes
- Handle navigation between routes
- Redirect users when necessary

This approach creates a robust and maintainable routing system that enhances the overall user experience of the application.