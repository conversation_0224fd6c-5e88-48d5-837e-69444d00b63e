# Stripe Webhook Troubleshooting

This document provides instructions for troubleshooting and deploying the Stripe webhook functions.

## Overview

We've created three versions of the Stripe webhook function:

1. **Original** (`stripe-webhook/index.ts`): The full implementation with all business logic.
2. **Minimal** (`stripe-webhook-minimal/index.ts`): A stripped-down version that only handles signature verification and basic event logging.
3. **Improved** (`stripe-webhook-improved/index.ts`): A middle-ground implementation that includes signature verification and essential payment processing logic.

## Current Issue

The webhook is being called 5 times in sequence, with the first call succeeding but the subsequent 4 calls failing with the error:

```
No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe?
```

This is a common issue with Stripe webhooks and is typically caused by one of the following:

1. **Request body consumption**: The request body stream is being consumed multiple times
2. **Shared state**: Using the same Stripe instance for multiple requests
3. **Body transformation**: The request body is being modified before verification

## Key Fixes in the Minimal Implementation

The minimal implementation includes several fixes to address these issues:

1. **Request cloning**: We clone the request before reading the body to ensure a fresh stream
   ```typescript
   const clonedRequest = req.clone();
   const body = await clonedRequest.text();
   ```

2. **Fresh Stripe instance**: We create a new Stripe instance for each request
   ```typescript
   function createStripeInstance(secretKey: string): Stripe {
     return new Stripe(secretKey, { apiVersion: '2024-11-20' });
   }
   ```

3. **Enhanced logging**: We log detailed information about the request body and verification process
   ```typescript
   console.log(`Body first 10 bytes [${requestId}]:`, Array.from(bodyBytes));
   console.log(`Verification inputs [${requestId}]:`);
   ```

4. **JSON validation**: We check if the body is valid JSON to help diagnose potential issues
   ```typescript
   try {
     const jsonBody = JSON.parse(body);
     console.log(`Body is valid JSON [${requestId}]: ${jsonBody.type || 'unknown type'}`);
   } catch (jsonErr) {
     console.error(`Body is not valid JSON [${requestId}]: ${jsonErr.message}`);
   }
   ```

## Deployment Instructions

### 1. Deploy the Minimal Version First

Start by deploying the minimal version to isolate and fix the signature verification issue:

```bash
cd /Users/<USER>/github/verbrauchsausweis-app
supabase functions deploy stripe-webhook-minimal
```

### 2. Update Stripe Webhook Endpoint

In your Stripe Dashboard:
1. Go to Developers > Webhooks
2. Update the webhook endpoint URL to point to your new function:
   `https://<your-project-ref>.supabase.co/functions/v1/stripe-webhook-minimal`
3. Make sure the webhook is configured with the correct events (e.g., `checkout.session.completed`)

### 3. Test the Minimal Version

Create a test checkout session in Stripe to trigger the webhook. Check the Supabase logs to see if the signature verification succeeds for all webhook calls.

### 4. Deploy the Improved Version

If the minimal version works correctly, update the improved version with the same fixes and deploy it:

```bash
supabase functions deploy stripe-webhook-improved
```

Update the Stripe webhook endpoint URL accordingly.

### 5. Return to the Original Implementation

Once you've confirmed the fixes work, update the original implementation with the same changes:

1. Clone the request before reading the body
2. Create a fresh Stripe instance for each request
3. Add enhanced logging for debugging

## Environment Variables

Ensure these environment variables are set correctly:

- `STRIPE_SECRET_KEY`: Your Stripe secret key (starts with `sk_`)
- `STRIPE_WEBHOOK_SECRET`: Your Stripe webhook signing secret (starts with `whsec_`)
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key

You can set these using the Supabase CLI:

```bash
supabase secrets set STRIPE_SECRET_KEY=sk_test_...
supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_...
```

## Common Issues and Solutions

### 1. Signature Verification Failure

- **Check webhook secret**: Ensure the `STRIPE_WEBHOOK_SECRET` matches exactly what's in your Stripe Dashboard.
- **Check for whitespace**: Make sure there's no extra whitespace in your secret.
- **Check API version**: Ensure you're using a compatible Stripe API version.

### 2. Request Body Handling

- The webhook function must read the request body exactly once.
- The body must be read as text, not JSON, for signature verification.
- Use `req.clone()` before reading the body if you need to access it multiple times.

### 3. Timestamp Tolerance

- Stripe signatures include a timestamp that must be within 5 minutes of the current time.
- Check your server's clock if you're consistently getting timestamp errors.

### 4. Multiple Webhook Calls

- Stripe may send the same webhook event multiple times to ensure delivery.
- Each call should be treated as a separate request with its own verification.
- Implement idempotency checks to prevent duplicate processing.

## Debugging Tips

1. Look for these specific log messages:
   - `✅ Signature verification successful` indicates success
   - `❌ Signature verification failed` indicates failure

2. Check the error message for clues:
   - `No signatures found matching the expected signature` often means the request body has been consumed or modified
   - `Timestamp outside the tolerance zone` means there's a time synchronization issue

3. Compare the request headers and body between successful and failed webhook calls.

4. Look at the request ID pattern to understand the sequence of webhook calls.

## Additional Resources

- [Stripe Webhook Documentation](https://stripe.com/docs/webhooks)
- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Stripe Signature Verification](https://stripe.com/docs/webhooks/signatures)
- [Handling Webhook Events Multiple Times](https://stripe.com/docs/webhooks/best-practices#duplicate-events)

